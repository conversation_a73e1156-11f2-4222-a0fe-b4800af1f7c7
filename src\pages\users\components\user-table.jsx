import { useGlobalFilter, usePagination, useSortBy, useTable } from 'react-table';
import { Button, Col, InputGroup, Row, Card, Form, ButtonGroup, OverlayTrigger, Tooltip } from 'react-bootstrap';
import { imagesData } from 'common/commonimages.jsx';
import { useEffect, useMemo } from 'react';

const COLUMNS5 = [
  {
    Header: 'نام کاربری',
    accessor: 'USERNAME',
    className: 'text-center ',
  },
  {
    Header: 'نام و نام خانوادگی',
    accessor: 'NAME',
    className: 'text-center ',
  },
  {
    Header: 'شماره تماس',
    accessor: 'PHONE',
    className: 'text-center ',
  },
  {
    Header: 'آخرین ورود',
    accessor: 'LAST',
    className: 'text-center ',
  },
  {
    Header: 'کارگروه',
    accessor: 'BADGE',
    className: 'text-center ',
  },
  {
    Header: 'تاریخ ثبت',
    accessor: 'DATE',
    className: 'text-center ',
  },
  {
    Header: 'اقدامات',
    accessor: 'ACTION',
    className: 'text-center ',
  },
];

// Shared actions block - now takes user and handlers as parameters
const actionButtons = (user, onEdit, onDelete, onSetInstitute) => (
  <span className="text-center align-middle">
    <ButtonGroup size="sm" className="flex-nowrap">
      {!user.institute && (
        <OverlayTrigger placement="top" overlay={<Tooltip>تخصیص کارگروه</Tooltip>}>
          <Button className="me-1" variant="info" onClick={() => onSetInstitute(user)}>
            تخصیص کارگروه
          </Button>
        </OverlayTrigger>
      )}
      <OverlayTrigger placement="top" overlay={<Tooltip>ویرایش</Tooltip>}>
        <Button className="me-1" onClick={() => onEdit(user)}>
          ویرایش
        </Button>
      </OverlayTrigger>
      <OverlayTrigger placement="top" overlay={<Tooltip>حذف</Tooltip>}>
        <Button onClick={() => onDelete(user)}>
          <i className="fa fa-trash"></i>
        </Button>
      </OverlayTrigger>
    </ButtonGroup>
  </span>
);

const DATATABLE5 = [
  // {
  //   PHOTO: <img src={imagesData("male1")} className="avatar avatar-md br-7" alt="profile" />,
  //   NAME: "علی رضایی",
  //   ROLE: "وزارتی",
  //   LAST: <span className="badge bg-light text-muted tx-13">۱۵ دقیقه پیش</span>,
  //   BADGE: <span className="badge font-weight-semibold bg-primary-transparent text-primary tx-11">مدیر پایگاه</span>,
  //   DATE: "۲۰ فروردین ۱۴۰۳",
  //   ACTION: actionButtons,
  // },
];

const GlobalFilter2 = ({ filter, setFilter }) => {
  return (
    <span className="d-flex" style={{ direction: 'rtl' }}>
      <input
        value={filter || ''}
        onChange={(e) => setFilter(e.target.value)}
        className="form-control"
        placeholder="جستجو..."
      />
    </span>
  );
};

const UserList = ({ users = [], loading = false, onEdit, onDelete, onSetInstitute }) => {
  // Transform users data to table format - memoized to prevent infinite re-renders
  const transformedData = useMemo(() => {
    return users.map((user) => ({
      USERNAME: user.username,
      NAME: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'نامشخص',
      PHONE: user.phone_number || '---',
      LAST: user.last_login ? (
        <span className="badge bg-success text-white tx-13">
          {new Date(user.last_login).toLocaleDateString('fa-IR')}
        </span>
      ) : (
        <span className="badge bg-light text-muted tx-13">هرگز</span>
      ),
      BADGE: (
        <span className="badge font-weight-semibold bg-primary-transparent text-primary tx-11">
          {user.institute?.title || '---'}
        </span>
      ),
      DATE: user.registration_date ? new Date(user.registration_date).toLocaleDateString('fa-IR') : 'نامشخص',
      ACTION: actionButtons(user, onEdit, onDelete, onSetInstitute),
    }));
  }, [users, onEdit, onDelete, onSetInstitute]);

  const tableInstance = useTable(
    {
      columns: COLUMNS5,
      data: transformedData,
    },
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const {
    getTableProps, // table props from react-table
    headerGroups, // headerGroups, if your table has groupings
    getTableBodyProps, // table body props from react-table
    prepareRow, // Prepare the row (this function needs to be called for each row before getting the row props)
    state,
    setGlobalFilter,
    page, // use, page or rows
    nextPage,
    previousPage,
    canNextPage,
    canPreviousPage,
    pageOptions,
    gotoPage,
    pageCount,
    setPageSize,
  } = tableInstance;

  const { globalFilter, pageIndex, pageSize } = state;

  return (
    <>
      <Row>
        <Col lg={12} xl={12}>
          <Card className="deleted-table">
            <InputGroup className="mb-4 mt-4 me-2" style={{ direction: 'ltr', justifyContent: 'end' }}>
              <GlobalFilter2 filter={globalFilter} setFilter={setGlobalFilter} />
              <InputGroup.Text className="btn btn-primary">
                <i className="fa fa-search" aria-hidden="true"></i>
              </InputGroup.Text>
            </InputGroup>
            <Card.Header className="border-bottom-0 p-4 d-flex justify-content-between">
              <Card.Title className="tx-13">
                {loading
                  ? 'در حال بارگذاری...'
                  : `ردیف ۱ تا ${Math.min(pageSize, users.length)} از ${users.length} کاربر`}
              </Card.Title>
              <div className="d-flex">
                <select
                  className="mb-6 table-border me-1"
                  value={pageSize}
                  onChange={(e) => setPageSize(Number(e.target.value))}
                >
                  {[10, 25, 50].map((pageSize) => (
                    <option key={pageSize} value={pageSize}>
                      نمایش {pageSize}
                    </option>
                  ))}
                </select>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="e-table pb-5" style={{ position: 'relative' }}>
                {loading && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(255, 255, 255, 0.8)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 10,
                    }}
                  >
                    <div className="spinner-border text-primary" role="status">
                      <span className="sr-only">در حال بارگذاری...</span>
                    </div>
                  </div>
                )}
                <div className="table-responsive ">
                  <table {...getTableProps()} className="table table-bordered text-nowrap mb-0">
                    <thead>
                      {headerGroups.map((headerGroup) => (
                        <tr {...headerGroup.getHeaderGroupProps()} key={Math.random()}>
                          {headerGroup.headers.map((column) => (
                            <th
                              {...column.getHeaderProps(column.getSortByToggleProps())}
                              className={column.className}
                              key={Math.random()}
                            >
                              <span className="tabletitle">{column.render('Header')}</span>
                            </th>
                          ))}
                        </tr>
                      ))}
                    </thead>
                    <tbody {...getTableBodyProps()}>
                      {page.map((row) => {
                        prepareRow(row);
                        return (
                          <tr className="text-center" {...row.getRowProps()} key={Math.random()}>
                            {row.cells.map((cell) => {
                              return (
                                <td {...cell.getCellProps()} key={Math.random()}>
                                  {cell.render('Cell')}
                                </td>
                              );
                            })}
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                  <div className="d-flex mt-4 align-items-center">
                    <span className="">
                      صفحه{' '}
                      <strong>
                        {pageIndex + 1} از {pageOptions.length}
                      </strong>{' '}
                    </span>
                    <span className="ms-auto ps-2">
                      <Button
                        variant=""
                        className="btn-default tablebutton me-2 my-2"
                        onClick={() => gotoPage(0)}
                        disabled={!canPreviousPage}
                      >
                        {' قبلی '}
                      </Button>
                      <Button
                        variant=""
                        className="btn-default tablebutton me-2 my-2"
                        onClick={() => {
                          previousPage();
                        }}
                        disabled={!canPreviousPage}
                      >
                        {' << '}
                      </Button>
                      <Button
                        variant=""
                        className="btn-default tablebutton me-2 my-2"
                        onClick={() => {
                          previousPage();
                        }}
                        disabled={!canPreviousPage}
                      >
                        {' < '}
                      </Button>
                      <Button
                        variant=""
                        className="btn-default tablebutton me-2 my-2"
                        onClick={() => {
                          nextPage();
                        }}
                        disabled={!canNextPage}
                      >
                        {' > '}
                      </Button>
                      <Button
                        variant=""
                        className="btn-default tablebutton me-2 my-2"
                        onClick={() => {
                          nextPage();
                        }}
                        disabled={!canNextPage}
                      >
                        {' >> '}
                      </Button>
                      <Button
                        variant=""
                        className="btn-default tablebutton me-2 my-2"
                        onClick={() => gotoPage(pageCount - 1)}
                        disabled={!canNextPage}
                      >
                        {' بعدی '}
                      </Button>
                    </span>
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default UserList;
