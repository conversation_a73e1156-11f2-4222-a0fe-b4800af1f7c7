import React, { useCallback, useState } from 'react';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';
import toastService from 'utils/toastService.js';
import DossierService from 'service/api/dossierService.js';
import Step1 from './steps/Step1';
import Step2 from './steps/Step2';
import Step3 from './steps/Step3';
import Step4 from './steps/Step4';
import Step5 from './steps/Step5';
import Step6 from './steps/Step6';
import { useNavigate } from 'react-router-dom';

const Wizard = ({ step: currentIndex, ...props }) => {
  const steps = React.Children.toArray(props.children);
  const prevStep = currentIndex !== 0 && steps[currentIndex - 1].props;
  const nextStep = currentIndex !== steps.length - 1 && steps[currentIndex + 1].props;

  return (
    <div>
      <nav className="btn-group steps basicsteps" style={{ padding: 20 }}>
        {steps.map((step, index) => (
          <Button
            key={step.props.number}
            onClick={() => props.onChange(index)}
            className={getClsNavBtn(index === currentIndex)}
            style={{ fontSize: '0.8rem' }}
          >
            <span className="number me-2 ">{step.props.number}</span>
            <strong>{step.props.title}</strong>
          </Button>
        ))}
      </nav>

      {steps[currentIndex]}

      <div className=" p-3 d-flex justify-content-between  ">
        <Button visible={prevStep} onClick={() => props.onChange(currentIndex - 1)} title={prevStep.description}>
          قبلی
        </Button>
        <Button visible={nextStep} onClick={() => props.onChange(currentIndex + 1)} title={nextStep.description}>
          بعدی
        </Button>
        {!nextStep && (
          <Button visible={!nextStep} onClick={() => props.onSubmit()} title="ثبت نهایی">
            ثبت نهایی
          </Button>
        )}
      </div>
    </div>
  );
};
const Step = ({ children }) => children;

function getClsNavBtn(active) {
  return 'btn' + (active ? ' active' : '');
}
function Button({ visible, ...props }) {
  return <button className={visible ? 'btn btn-primary ' : 'invisible'} {...props} />;
}

const FormWizard = ({ isEditMode = false }) => {
  const { step, dossierUuid, step1, step2, step3, step4, step5, step6, setStep, setDossierUuid } =
    useSubmitDocumentFormStore();

  const [validationErrors, setValidationErrors] = useState({});
  const [validatedSteps, setValidatedSteps] = useState([]);
  const [loading, setLoading] = useState(false);

  const navigate = useNavigate();

  // Transform form data to API format
  const transformToApiFormat = useCallback(() => {
    if (step === 1)
      return {
        reporter: {
          full_name: step2.reporterFullname || '',
          gender: step2.reporterGender || 'male',
          national_id: step2.reporterNationalID || '',
          phone_number: step2.reporterPhone || '',
          email: step2.reporterEmail || '',
          institute: step2.reporterOrgan || '',
          other_institute: step2.reporterCustomOrgan || '',
          role_type: step2.reporterRole || 'faculty_member',
          academic_rank: step2.reporterGrade || '',
          field_of_study: step2.reporterField || '',
          years_of_service: parseInt(step2.reporterYearsOfService) || 0,
          academic_level: step2.reporterEducationLevel || 'phd',
          academic_status: step2.reporterAcademicStatus || 'graduated',
          executive_position: step2.reporterAdministrativePosition || '',
        },
      };
    else if (step === 2)
      return {
        reportee: {
          full_name: step3.reportedFullname || '',
          gender: step3.reportedGender || 'male',
          national_id: step3.reportedNationalID || '',
          phone_number: step3.reportedPhone || '',
          email: step3.reportedEmail || '',
          institute: step3.reportedOrgan || '',
          other_institute: step3.reportedCustomOrgan || '',
          role_type: step3.reportedRole || 'faculty_member',
          academic_rank: step3.reportedGrade || '',
          field_of_study: step3.reportedField || '',
          years_of_service: parseInt(step3.reportedYearsOfService) || 0,
          academic_level: step3.reportedEducationLevel || 'phd',
          academic_status: step3.reportedAcademicStatus || 'graduated',
          executive_position: step3.reportedAdministrativePosition || '',
          persons: {},
        },
        violation_history: {
          has_violation: step3.reportedHasViolation === 'yes',
          ...(step3.reportedHasViolation === 'yes' && {
            is_repeated: step3.reportedRepeatViolation === 'yes',
            violation_types: step3.reportedViolationTypes ? [step3.reportedViolationTypes] : [],
            multiple_institutes: step3.reportedViolationMultipleInstitutes === 'yes',
          }),
        },
        violation_locations: (step3.reportedViolationInstitutes || []).map((institute) => ({
          institute: institute.university || '',
          university_type: institute.universityType || 'type1',
          institute_type: institute.instituteType || 'faculty',
          location: institute.location || 'domestic',
          has_ethics_committee: institute.hasEthicsCommittee === 'yes',
          reportee_organizational_relation: institute.reportedRelation || 'faculty',
          reporter_organizational_relation: institute.reporterRelation || 'faculty',
        })),
      };
    else if (step === 3)
      return {
        report_method: {
          report_date: step4.reportReceivedDate || new Date().toISOString().split('T')[0],
          report_delay: step4.reportDelay || 'less_than_one_year',
          report_source: step4.reportMethod || 'self_report',
          whistleblower_source: step4.whistleblowerSource || '',
          reporter_identity: step4.reporterIdentity || 'known',
          reporter_role: step4.reporterViolationRole || 'whistleblower',
          reporter_stakeholder_status: step4.reporterStakeholder || 'stakeholder',
          reporter_reportee_relationship: step4.reporterReportedRelation || 'professor_student',
          receiving_authority: step4.reportRecipient || 'institute_ethics_committee',
          report_format: step4.reportFormat || 'written',
          report_channel: step4.reportChannel || 'phone_call',
          research_stage: step4.researchStage || 'before_research',
        },
      };
    else if (step === 4)
      return {
        reporter_description: step5.reporterDescription || '',
      };
    else if (step === 5)
      return {
        reportee_description: step6.reportedDefenseDescription || '',
      };
  }, [step2, step3, step4, step5, step6]);

  const validateStep = useCallback(
    (currentStep) => {
      const errors = {};
      let isValid = true;

      switch (currentStep) {
        case 0: // Step 1: کارگروه دریافت کننده گزارش
          if (!step1.organ) {
            errors.organ = 'لطفاً کارگروه را انتخاب کنید';
            isValid = false;
          }
          if (step1.organ === 'other' && !step1.customOrgan) {
            errors.customOrgan = 'لطفاً نام کارگروه را وارد کنید';
            isValid = false;
          }
          break;

        case 1: // Step 2: گزارش دهنده
          if (!step2.reporterFullname) {
            errors.reporterFullname = 'لطفاً نام و نام خانوادگی را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterGender) {
            errors.reporterGender = 'لطفاً جنسیت را انتخاب کنید';
            isValid = false;
          }
          if (!step2.reporterNationalID) {
            errors.reporterNationalID = 'لطفاً کد ملی را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterPhone) {
            errors.reporterPhone = 'لطفاً شماره تماس را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterEmail) {
            errors.reporterEmail = 'لطفاً رایانامه را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterOrgan) {
            errors.reporterOrgan = 'لطفاً موسسه محل خدمت را انتخاب کنید';
            isValid = false;
          }
          if (step2.reporterOrgan === 'other' && !step2.reporterCustomOrgan) {
            errors.reporterCustomOrgan = 'لطفاً نام موسسه را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterRole) {
            errors.reporterRole = 'لطفاً نقش حرفه‌ای و آکادمیک را انتخاب کنید';
            isValid = false;
          }
          if (step2.reporterRole === 'faculty_member') {
            if (!step2.reporterGrade) {
              errors.reporterGrade = 'لطفاً مرتبه علمی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterField) {
              errors.reporterField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterYearsOfService) {
              errors.reporterYearsOfService = 'لطفاً سنوات خدمت را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step2.reporterRole === 'student') {
            if (!step2.reporterField) {
              errors.reporterField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
            if (!step2.reporterAcademicStatus) {
              errors.reporterAcademicStatus = 'لطفاً وضعیت تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step2.reporterRole === 'non_faculty_member') {
            if (!step2.reporterAdministrativePosition) {
              errors.reporterAdministrativePosition = 'لطفاً سمت اجرایی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step2.reporterRole === 'independent_researcher') {
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          break;

        case 2: // Step 3: گزارش شونده
          if (!step3.reportedFullname) {
            errors.reportedFullname = 'لطفاً نام و نام خانوادگی را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedGender) {
            errors.reportedGender = 'لطفاً جنسیت را انتخاب کنید';
            isValid = false;
          }
          if (!step3.reportedNationalID) {
            errors.reportedNationalID = 'لطفاً کد ملی را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedPhone) {
            errors.reportedPhone = 'لطفاً شماره تماس را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedEmail) {
            errors.reportedEmail = 'لطفاً رایانامه را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedOrgan) {
            errors.reportedOrgan = 'لطفاً موسسه محل خدمت را انتخاب کنید';
            isValid = false;
          }
          if (step3.reportedOrgan === 'other' && !step3.reportedCustomOrgan) {
            errors.reportedCustomOrgan = 'لطفاً نام موسسه را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedRole) {
            errors.reportedRole = 'لطفاً نقش حرفه‌ای و آکادمیک را انتخاب کنید';
            isValid = false;
          }
          if (step3.reportedRole === 'faculty_member') {
            if (!step3.reportedGrade) {
              errors.reportedGrade = 'لطفاً مرتبه علمی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedField) {
              errors.reportedField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedYearsOfService) {
              errors.reportedYearsOfService = 'لطفاً سنوات خدمت را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step3.reportedRole === 'student') {
            if (!step3.reportedField) {
              errors.reportedField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
            if (!step3.reportedAcademicStatus) {
              errors.reportedAcademicStatus = 'لطفاً وضعیت تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step3.reportedRole === 'non_faculty_member') {
            if (!step3.reportedAdministrativePosition) {
              errors.reportedAdministrativePosition = 'لطفاً سمت اجرایی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (
            [
              'independent_researcher',
              'institute_president',
              'institute_vice_president',
              'board_member',
              'trustee_member',
              'ethics_committee_member',
            ].includes(step3.reportedRole)
          ) {
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (!step3.reportedHasViolation) {
            errors.reportedHasViolation = 'لطفاً سابقه تخلف پژوهشی را مشخص کنید';
            isValid = false;
          }
          if (step3.reportedHasViolation === 'yes') {
            if (!step3.reportedRepeatViolation) {
              errors.reportedRepeatViolation = 'لطفاً تکرار تخلفات قبلی را مشخص کنید';
              isValid = false;
            }
            if (!step3.reportedViolationTypes) {
              errors.reportedViolationTypes = 'لطفاً انواع تخلفات را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedMultiInstituteViolations) {
              errors.reportedMultiInstituteViolations = 'لطفاً مشخص کنید تخلفات در چند موسسه بوده‌اند';
              isValid = false;
            }
            if (!step3.reportedViolationCases) {
              errors.reportedViolationCases = 'لطفاً شماره پرونده‌های تخلفات قبلی را وارد کنید';
              isValid = false;
            }
          }
          if (!step3.reportedViolationMultipleInstitutes) {
            errors.reportedViolationMultipleInstitutes = 'لطفاً مشخص کنید تخلف در چند موسسه واقع شده است';
            isValid = false;
          }
          if (step3.reportedViolationInstitutes && Array.isArray(step3.reportedViolationInstitutes)) {
            step3.reportedViolationInstitutes.forEach((institute, index) => {
              if (!institute.university) {
                errors[`institute_university_${index}`] = 'لطفاً نام دانشگاه را انتخاب کنید';
                isValid = false;
              }
              if (institute.university === 'other' && !institute.customUniversity) {
                errors[`institute_customUniversity_${index}`] = 'لطفاً نام موسسه را وارد کنید';
                isValid = false;
              }
              if (!institute.universityType) {
                errors[`institute_universityType_${index}`] = 'لطفاً سطح تیپ دانشگاه را انتخاب کنید';
                isValid = false;
              }
              if (!institute.instituteType) {
                errors[`institute_instituteType_${index}`] = 'لطفاً نوع موسسه را انتخاب کنید';
                isValid = false;
              }
              if (!institute.location) {
                errors[`institute_location_${index}`] = 'لطفاً محل جغرافیایی موسسه را مشخص کنید';
                isValid = false;
              }
              if (!institute.hasEthicsCommittee) {
                errors[`institute_hasEthicsCommittee_${index}`] = 'لطفاً مشخص کنید موسسه دارای کارگروه اخلاق است';
                isValid = false;
              }
              if (!institute.reportedRelation) {
                errors[`institute_reportedRelation_${index}`] = 'لطفاً رابطه سازمانی گزارش‌شونده را انتخاب کنید';
                isValid = false;
              }
              if (!institute.reporterRelation) {
                errors[`institute_reporterRelation_${index}`] = 'لطفاً رابطه سازمانی گزارش‌دهنده را انتخاب کنید';
                isValid = false;
              }
            });
          }
          break;

        case 3: // Step 4: نحوه گزارش تخلف
          if (!step4.reportReceivedDate) {
            errors.reportReceivedDate = 'لطفاً تاریخ دریافت گزارش را وارد کنید';
            isValid = false;
          }
          if (!step4.reportDelay) {
            errors.reportDelay = 'لطفاً مدت زمان گزارش را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reportMethod) {
            errors.reportMethod = 'لطفاً نحوه اعلام گزارش را مشخص کنید';
            isValid = false;
          }
          if (step4.reportMethod === 'whistleblower' && !step4.whistleblowerSource) {
            errors.whistleblowerSource = 'لطفاً منبع افشاگری را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reporterIdentity) {
            errors.reporterIdentity = 'لطفاً هویت گزارش‌دهنده را مشخص کنید';
            isValid = false;
          }
          if (!step4.reporterViolationRole) {
            errors.reporterViolationRole = 'لطفاً نقش گزارش‌دهنده را مشخص کنید';
            isValid = false;
          }
          if (!step4.reporterStakeholder) {
            errors.reporterStakeholder = 'لطفاً ذینفعی گزارش‌دهنده را مشخص کنید';
            isValid = false;
          }
          if (!step4.reporterReportedRelation) {
            errors.reporterReportedRelation = 'لطفاً رابطه گزارش‌دهنده با گزارش‌شونده را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reportRecipient) {
            errors.reportRecipient = 'لطفاً مرجع دریافت‌کننده گزارش را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reportFormat) {
            errors.reportFormat = 'لطفاً شکل گزارش تخلف را مشخص کنید';
            isValid = false;
          }
          if (!step4.reportChannel) {
            errors.reportChannel = 'لطفاً مسیر ارسال گزارش را انتخاب کنید';
            isValid = false;
          }
          if (!step4.researchStage) {
            errors.researchStage = 'لطفاً مرحله پژوهش را انتخاب کنید';
            isValid = false;
          }
          break;

        case 4: // Step 5: شرح تخلف توسط گزارش‌دهنده
          if (!step5.reporterDescription) {
            errors.reporterDescription = 'لطفاً شرح تخلف را وارد کنید';
            isValid = false;
          }
          break;

        case 5: // Step 6: شرح تخلف توسط گزارش‌شونده
          if (!step6.reportedDefenseDescription) {
            errors.reportedDefenseDescription = 'لطفاً توضیحات و دفاعیات را وارد کنید';
            isValid = false;
          }
          break;

        default:
          isValid = false;
      }

      setValidationErrors(errors);
      return isValid;
    },
    [step1, step2, step3, step4, step5, step6]
  );

  // Helper function to parse API errors and map them to form field names
  const parseApiErrors = (apiErrorData) => {
    const errors = {};

    if (!apiErrorData) return errors;

    // Handle both array and object formats
    const errorData = Array.isArray(apiErrorData) ? apiErrorData[0] : apiErrorData;

    if (!errorData || typeof errorData !== 'object') return errors;

    // Debug log to see the structure
    console.log('Parsing API errors:', errorData);

    // Handle reporter errors
    if (errorData.reporter && typeof errorData.reporter === 'object') {
      Object.keys(errorData.reporter).forEach((field) => {
        const messages = errorData.reporter[field];
        if (Array.isArray(messages) && messages.length > 0) {
          // Map API field names to form field names
          const fieldMapping = {
            national_id: 'reporterNationalID',
            phone_number: 'reporterPhone',
            email: 'reporterEmail',
            institute: 'reporterOrgan',
            full_name: 'reporterFullname',
            gender: 'reporterGender',
            role_type: 'reporterRole',
            academic_rank: 'reporterGrade',
            field_of_study: 'reporterField',
            years_of_service: 'reporterYearsOfService',
            academic_level: 'reporterEducationLevel',
            academic_status: 'reporterAcademicStatus',
            executive_position: 'reporterAdministrativePosition',
            other_institute: 'reporterCustomOrgan',
          };

          const formFieldName = fieldMapping[field] || field;
          errors[formFieldName] = messages[0]; // Use the first error message
        }
      });
    }

    // Handle reportee errors
    if (errorData.reportee && typeof errorData.reportee === 'object') {
      Object.keys(errorData.reportee).forEach((field) => {
        const messages = errorData.reportee[field];
        if (Array.isArray(messages) && messages.length > 0) {
          // Map API field names to form field names
          const fieldMapping = {
            national_id: 'reportedNationalID',
            phone_number: 'reportedPhone',
            email: 'reportedEmail',
            institute: 'reportedOrgan',
            full_name: 'reportedFullname',
            gender: 'reportedGender',
            role_type: 'reportedRole',
            academic_rank: 'reportedGrade',
            field_of_study: 'reportedField',
            years_of_service: 'reportedYearsOfService',
            academic_level: 'reportedEducationLevel',
            academic_status: 'reportedAcademicStatus',
            executive_position: 'reportedAdministrativePosition',
            other_institute: 'reportedCustomOrgan',
          };

          const formFieldName = fieldMapping[field] || field;
          errors[formFieldName] = messages[0]; // Use the first error message
        }
      });
    }

    // Handle other error types (report_method, etc.)
    Object.keys(errorData).forEach((section) => {
      if (section !== 'reporter' && section !== 'reportee' && typeof errorData[section] === 'object') {
        Object.keys(errorData[section]).forEach((field) => {
          const messages = errorData[section][field];
          if (Array.isArray(messages) && messages.length > 0) {
            errors[field] = messages[0];
          }
        });
      }
    });

    return errors;
  };

  const handleStep = async (nextStep) => {
    if (nextStep === step) return;
    if (validatedSteps.includes(nextStep)) {
      setStep(nextStep);
      return;
    }

    if (!validateStep(step)) {
      return; // Don't proceed if validation fails
    }

    try {
      setLoading(true);
      // Clear previous API errors
      setValidationErrors({});

      // Step 0 -> 1: Create new dossier (only in create mode)
      if (step === 0 && nextStep === 1 && !isEditMode) {
        if (!step1.organ) {
          toastService.error('لطفاً کارگروه را انتخاب کنید');
          return;
        }

        const newDossierData = {
          institute: step1.organ === 'other' ? step1.customOrgan : step1.organ,
        };

        const response = await DossierService.addNewDossier(newDossierData);
        const uuid = response?.data?.data?.dossier_id;

        if (uuid) {
          setDossierUuid(uuid);
          toastService.success('پرونده جدید ایجاد شد');
        } else {
          throw new Error('UUID not received from server');
        }
      }

      // In edit mode, skip dossier creation for step 0 -> 1
      else if (step === 0 && nextStep === 1 && isEditMode) {
        if (!step1.organ) {
          toastService.error('لطفاً کارگروه را انتخاب کنید');
          return;
        }
        // Just validate and proceed, dossier already exists
      }

      // Steps 1+ -> Update existing dossier
      else if (step >= 1 && dossierUuid) {
        const updateData = transformToApiFormat();
        await DossierService.updateDossier(dossierUuid, updateData);
        toastService.success('اطلاعات به‌روزرسانی شد');
      }

      setValidatedSteps([...validatedSteps, step]);
      setStep(nextStep);
    } catch (error) {
      console.error('Error in handleStep:', error);

      // Handle API validation errors
      if (error.response && error.response.data && error.response.data.data) {
        const apiErrors = parseApiErrors(error.response.data.data);
        setValidationErrors(apiErrors);
        toastService.error('لطفاً خطاهای فرم را بررسی کنید');
      } else {
        toastService.error('خطا در ذخیره اطلاعات');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep(step)) {
      return; // Don't proceed if validation fails
    }

    try {
      if (loading) return;
      setLoading(true);
      // Clear previous API errors
      setValidationErrors({});

      if (!dossierUuid) {
        toastService.error('خطا: شناسه پرونده موجود نیست');
        return;
      }

      // Final update with all data
      const finalData = transformToApiFormat();
      await DossierService.updateDossier(dossierUuid, finalData);
      await DossierService.submitDossier(dossierUuid, { description: '---' });

      toastService.success(isEditMode ? 'پرونده با موفقیت به‌روزرسانی شد!' : 'پرونده با موفقیت ثبت شد!');
      return navigate('/app/inquiry');
    } catch (error) {
      console.error('Error in final submit:', error);

      // Handle API validation errors
      if (error.response && error.response.data && error.response.data.data) {
        const apiErrors = parseApiErrors(error.response.data.data);
        setValidationErrors(apiErrors);
        toastService.error('لطفاً خطاهای فرم را بررسی کنید');
      } else {
        toastService.error('خطا در ثبت نهایی پرونده!');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Wizard step={step} onChange={handleStep} onSubmit={handleSubmit}>
      <Step title="کارگروه دریافت کننده گزارش" number="۱">
        <Step1 validationErrors={validationErrors} />
      </Step>
      <Step title="گزارش دهنده" number="۲">
        <Step2 validationErrors={validationErrors} />
      </Step>
      <Step title="گزارش شونده" number="۳">
        <Step3 validationErrors={validationErrors} />
      </Step>
      <Step title="نحوه گزارش تخلف" number="۴">
        <Step4 validationErrors={validationErrors} />
      </Step>
      <Step title="شرح تخلف توسط گزارش‌دهنده" number="۵">
        <Step5 validationErrors={validationErrors} />
      </Step>
      <Step title="شرح تخلف توسط گزارش شونده" number="۶">
        <Step6 validationErrors={validationErrors} />
      </Step>
    </Wizard>
  );
};

FormWizard.propTypes = {};

export default FormWizard;
