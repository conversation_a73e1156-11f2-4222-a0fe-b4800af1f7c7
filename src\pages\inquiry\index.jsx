import { useState, Fragment, useEffect, useCallback, useContext } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Card, Col, Row, Form, Button, Badge, Table, ButtonGroup, OverlayTrigger, Tooltip } from 'react-bootstrap';
import Select from 'react-select';
import { convertToPersianNumbers } from 'utils/helper.js';
import DossierService from 'service/api/dossierService.js';
import useGeneralStore from 'store/generalStore.js';
import toastService from 'utils/toastService.js';
import moment from 'moment-jalaali';
import AuthContext from 'context/auth-context.jsx';

// Status configuration
const STATUS_CONFIG = {
  draft: { label: 'پیش نویس', color: 'secondary' },
  expert_review: { label: 'داوری و کارشناسی', color: 'primary' },
  initial_opinion: { label: 'صدور نظریه بدوی کارگروه موسسه', color: 'info' },
  appeal_request: { label: 'درخواست تجدید نظر و ارسال به کارگروه وزارتی', color: 'warning' },
  ministry_opinion: { label: 'صدور نظریه کارگروه وزارتی', color: 'orange' },
  final_opinion: { label: 'صدور نظریه نهایی کارگروه موسسه', color: 'teal' },
  violation_details: { label: 'احراز تخلف', color: 'teal' },
  referred: { label: 'ارجاع به مراجع ذیصلاح', color: 'danger' },
  ruling_issued: { label: 'صدور حکم/رای مراجع ذیصلاح', color: 'purple' },
  completed: { label: 'خاتمه یافته', color: 'dark' },
};

const Inquiry = () => {
  const navigate = useNavigate();
  const { setData } = useGeneralStore();
  const location = useLocation();
  const { filter } = location.state || {};
  const { profile } = useContext(AuthContext);

  useEffect(() => {
    setData({ pageTitle: 'جستجو پرونده‌ها' });
  }, [setData]);

  // State management
  const [dossiers, setDossiers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({
    q: '',
    dossier_code: '',
    institute_name: '',
    reporter_name: '',
    reportee_name: '',
    status: '',
    page: 1,
    page_size: 10,
  });

  // Fetch dossiers from API
  const fetchDossiers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await DossierService.getList();
      const dossiersData = response?.data?.data?.dossiers || [];

      // Apply client-side filtering if needed
      let filteredDossiers = dossiersData;

      // General search
      if (filters.q) {
        filteredDossiers = filteredDossiers.filter(
          (dossier) =>
            dossier.dossier_code?.toLowerCase().includes(filters.q.toLowerCase()) ||
            dossier.institute?.toLowerCase().includes(filters.q.toLowerCase()) ||
            dossier.reporter?.full_name?.toLowerCase().includes(filters.q.toLowerCase()) ||
            dossier.reportee?.full_name?.toLowerCase().includes(filters.q.toLowerCase())
        );
      }

      // Specific field searches
      if (filters.dossier_code) {
        filteredDossiers = filteredDossiers.filter((dossier) =>
          dossier.dossier_code?.toLowerCase().includes(filters.dossier_code.toLowerCase())
        );
      }

      if (filters.institute_name) {
        filteredDossiers = filteredDossiers.filter((dossier) =>
          dossier.institute?.toLowerCase().includes(filters.institute_name.toLowerCase())
        );
      }

      if (filters.reporter_name) {
        filteredDossiers = filteredDossiers.filter((dossier) =>
          dossier.reporter?.full_name?.toLowerCase().includes(filters.reporter_name.toLowerCase())
        );
      }

      if (filters.reportee_name) {
        filteredDossiers = filteredDossiers.filter((dossier) =>
          dossier.reportee?.full_name?.toLowerCase().includes(filters.reportee_name.toLowerCase())
        );
      }

      if (filters.status) {
        filteredDossiers = filteredDossiers.filter((dossier) => dossier.dossier_status === filters.status);
      }

      setDossiers(filteredDossiers);
      setTotal(filteredDossiers.length);
    } catch (error) {
      console.error('Error fetching dossiers:', error);
      toastService.error('خطا در دریافت لیست پرونده‌ها');
      setDossiers([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchDossiers();
  }, [fetchDossiers]);

  // Get status options for Select component
  const getStatusOptions = () => {
    const options = [{ value: '', label: 'همه وضعیت‌ها' }];
    Object.entries(STATUS_CONFIG).forEach(([key, config]) => {
      options.push({ value: key, label: config.label });
    });
    return options;
  };

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
      page: 1, // Reset to first page when filtering
    }));
  };

  // Handle status filter change for Select component
  const handleStatusChange = (selectedOption) => {
    const value = selectedOption ? selectedOption.value : '';
    handleFilterChange('status', value);
  };

  // Handle search
  const handleSearch = () => {
    fetchDossiers();
  };

  // Clear filters
  const handleClearFilters = () => {
    setFilters({
      q: '',
      dossier_code: '',
      institute_name: '',
      reporter_name: '',
      reportee_name: '',
      status: '',
      page: 1,
      page_size: 10,
    });
  };

  // Format date to Jalali
  const formatJalaliDate = (dateString) => {
    if (!dateString) return 'نامشخص';
    return convertToPersianNumbers(moment(dateString).format('jYYYY/jMM/jDD HH:mm'));
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const config = STATUS_CONFIG[status] || { label: status, color: 'secondary' };
    return (
      <Badge bg={config.color} className="tx-11">
        {config.label}
      </Badge>
    );
  };

  // Handle actions based on user permissions and dossier status
  const canPerformAction = (action, dossier) => {
    if (!profile) return false;

    const status = dossier.dossier_status;
    const isMinisterial = profile.is_ministerial;
    const userInstituteId = profile.institute?.title;
    const dossierInstituteId = dossier.institute?.uuid || dossier.institute;

    // Check if user created this dossier (belongs to same institute)
    const isOwnDossier = userInstituteId === dossierInstituteId;

    switch (action) {
      case 'view':
        // Everyone can view non-draft dossiers
        return status !== 'draft';

      case 'edit':
        // Only own dossiers in draft status can be edited
        return isOwnDossier && status === 'draft';

      case 'delete':
        // Only own dossiers in draft status can be deleted
        return isOwnDossier && status === 'draft';

      case 'expert_review':
        // Own dossiers can perform expert review
        return isOwnDossier && status === 'expert_review';

      case 'initial_opinion':
        // Own dossiers can issue initial opinion
        return isOwnDossier && status === 'initial_opinion';

      case 'appeal_request':
        // Own dossiers can handle appeal requests
        return isOwnDossier && status === 'appeal_request';

      case 'ministry_opinion':
        // Only ministerial users can issue ministry opinion
        // If ministerial user created the dossier, they can edit ministry opinion
        // If dossier belongs to another institute, ministerial user can also edit ministry opinion
        if (!isMinisterial) return false;
        return status === 'ministry_opinion';

      case 'final_opinion':
        // Own dossiers can issue final opinion
        return isOwnDossier && status === 'final_opinion';

      case 'violation_details':
        // Own dossiers can handle violation details
        return isOwnDossier && status === 'violation_details';

      case 'referred':
        // Own dossiers can handle referrals
        return isOwnDossier && status === 'referred';

      case 'ruling_issued':
        // Own dossiers can handle ruling issued
        return isOwnDossier && status === 'ruling_issued';

      default:
        return false;
    }
  };

  // Action handlers
  const handleView = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/view`);
  };

  const handleEdit = (dossier) => {
    navigate(`/app/submit-document/${dossier.uuid}`);
  };

  const handleExpertReview = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/expert-review`);
  };

  const handleInitialOpinion = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/initial-opinion`);
  };

  const handleAppealRequest = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/appeal-request`);
  };

  const handleMinistryOpinion = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/ministry-opinion`);
  };

  const handleFinalOpinion = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/final-opinion`);
  };

  const handleViolationDetails = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/violation-details`);
  };

  const handleReferred = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/referred`);
  };

  const handleRulingIssued = (dossier) => {
    navigate(`/app/dossier/${dossier.uuid}/ruling-issued`);
  };

  const handleDelete = async (dossier) => {
    if (window.confirm('آیا از حذف این پرونده اطمینان دارید؟')) {
      try {
        await DossierService.deleteDossier(dossier.uuid);
        toastService.success('پرونده با موفقیت حذف شد');
        fetchDossiers();
      } catch (error) {
        toastService.error(error?.response?.data?.data?.[0] || 'خطا در حذف پرونده');
      }
    }
  };

  // Handle initial filters from navigation state
  useEffect(() => {
    if (filter) {
      setFilters((prev) => ({
        ...prev,
        ...filter,
      }));
    }
  }, [filter]);

  return (
    <Fragment>
      {/* Filters Section */}
      <Row className="row-sm mb-3">
        <Col xl={12}>
          <Card>
            <Card.Header className="border-bottom-0 d-flex justify-content-between align-items-center">
              <h3 className="card-title mb-0">
                <i className="fe fe-filter tx-primary mg-r-5" />
                فیلترهای جستجو
              </h3>
              <Badge bg="primary" className="tx-12">
                {total} پرونده
              </Badge>
            </Card.Header>
            <Card.Body className="p-3">
              <Row className="align-items-end mb-3">
                <Col md={12}>
                  <Form.Group>
                    <Form.Label>جستجوی عمومی</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="جستجو در تمام فیلدها..."
                      value={filters.q}
                      onChange={(e) => handleFilterChange('q', e.target.value)}
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="align-items-end mb-3">
                <Col md={3}>
                  <Form.Group>
                    <Form.Label>شماره پرونده</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="شماره پرونده..."
                      value={filters.dossier_code}
                      onChange={(e) => handleFilterChange('dossier_code', e.target.value)}
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label>نام موسسه</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="نام موسسه..."
                      value={filters.institute_name}
                      onChange={(e) => handleFilterChange('institute_name', e.target.value)}
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label>نام گزارش دهنده</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="نام گزارش دهنده..."
                      value={filters.reporter_name}
                      onChange={(e) => handleFilterChange('reporter_name', e.target.value)}
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label>نام گزارش گیرنده</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="نام گزارش گیرنده..."
                      value={filters.reportee_name}
                      onChange={(e) => handleFilterChange('reportee_name', e.target.value)}
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="align-items-end">
                <Col md={4}>
                  <Form.Group>
                    <Form.Label>وضعیت</Form.Label>
                    <Select
                      options={getStatusOptions()}
                      placeholder="انتخاب وضعیت..."
                      classNamePrefix="Select2"
                      value={getStatusOptions().find((option) => option.value === filters.status) || null}
                      onChange={handleStatusChange}
                      noOptionsMessage={() => 'گزینه‌ای موجود نیست'}
                      isSearchable
                      isClearable
                    />
                  </Form.Group>
                </Col>

                <Col md={8}>
                  <div className="d-flex gap-2">
                    <Button variant="primary" onClick={handleSearch} disabled={loading}>
                      {loading ? <i className="fe fe-loader fa-spin"></i> : <i className="fe fe-search"></i>}
                      {' جستجو'}
                    </Button>
                    <Button variant="outline-secondary" onClick={handleClearFilters}>
                      <i className="fe fe-x"></i>
                      {' پاک کردن فیلتر'}
                    </Button>
                  </div>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Dossiers Table */}
      <Row className="row-sm">
        <Col xl={12}>
          <Card className="overflow-hidden">
            <Card.Header className="border-bottom-0 d-flex justify-content-between align-items-center">
              <h3 className="card-title mb-0">
                <i className="fe fe-file-text tx-primary mg-r-5" />
                لیست پرونده‌ها
              </h3>
              {loading && (
                <div className="d-flex align-items-center">
                  <i className="fe fe-loader fa-spin mg-r-5"></i>
                  <span className="tx-12">در حال بارگذاری...</span>
                </div>
              )}
            </Card.Header>
            <Card.Body className="p-0">
              {dossiers.length === 0 && !loading ? (
                <div className="text-center p-5">
                  <i className="fe fe-file-text tx-100 tx-muted mb-3"></i>
                  <h4 className="tx-muted">هیچ پرونده‌ای یافت نشد</h4>
                  <p className="tx-muted">لطفاً فیلترهای جستجو را تغییر دهید یا پرونده جدیدی ایجاد کنید.</p>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table className="table table-bordered text-nowrap mb-0">
                    <thead>
                      <tr>
                        <th className="wd-15p">شماره پرونده</th>
                        <th className="wd-15p">موسسه</th>
                        <th className="wd-15p">تاریخ ایجاد</th>
                        <th className="wd-15p">آخرین تغییر</th>
                        <th className="wd-15p">گزارش‌دهنده</th>
                        <th className="wd-10p">وضعیت</th>
                        <th className="wd-15p text-center">عملیات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {dossiers.map((dossier, index) => (
                        <tr key={dossier.uuid || index}>
                          <td>
                            <span className="tx-13">{dossier.dossier_code || '---'}</span>
                          </td>
                          <td>
                            <span className="tx-13">{dossier.institute || 'نامشخص'}</span>
                          </td>
                          <td>
                            <span className="tx-12 text-muted">{formatJalaliDate(dossier.created_at)}</span>
                          </td>
                          <td>
                            <span className="tx-12 text-muted">{formatJalaliDate(dossier.updated_at)}</span>
                          </td>
                          <td>
                            <span className="tx-13">{dossier.reporter?.full_name || 'نامشخص'}</span>
                          </td>
                          {/* <td>
                            <span className="tx-13">{dossier.reportee?.full_name || 'نامشخص'}</span>
                          </td> */}
                          <td className="text-center">{getStatusBadge(dossier.dossier_status)}</td>
                          <td className="text-center">
                            <ButtonGroup size="sm" style={{ display: 'flex', justifyContent: 'center', gap: '6px' }}>
                              {canPerformAction('view', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>مشاهده</Tooltip>}>
                                  <Button
                                    variant="outline-primary"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleView(dossier)}
                                  >
                                    <i className="fe fe-eye"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('edit', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>ویرایش</Tooltip>}>
                                  <Button
                                    variant="outline-warning"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleEdit(dossier)}
                                  >
                                    <i className="fe fe-edit"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('delete', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>حذف</Tooltip>}>
                                  <Button
                                    variant="outline-danger"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleDelete(dossier)}
                                  >
                                    <i className="fe fe-trash"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('expert_review', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>کارشناسی</Tooltip>}>
                                  <Button
                                    variant="outline-info"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleExpertReview(dossier)}
                                  >
                                    <i className="fe fe-search"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}

                              {canPerformAction('initial_opinion', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>نظریه بدوی</Tooltip>}>
                                  <Button
                                    variant="outline-info"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleInitialOpinion(dossier)}
                                  >
                                    <i className="fe fe-file-text"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('appeal_request', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>درخواست تجدیدنظر</Tooltip>}>
                                  <Button
                                    variant="outline-warning"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleAppealRequest(dossier)}
                                  >
                                    <i className="fe fe-refresh-cw"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('ministry_opinion', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>نظریه وزارتی</Tooltip>}>
                                  <Button
                                    variant="outline-primary"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleMinistryOpinion(dossier)}
                                  >
                                    <i className="fe fe-award"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('final_opinion', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>نظریه نهایی</Tooltip>}>
                                  <Button
                                    variant="outline-info"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleFinalOpinion(dossier)}
                                  >
                                    <i className="fe fe-check-circle"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('violation_details', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>احراز تخلف</Tooltip>}>
                                  <Button
                                    variant="outline-info"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleViolationDetails(dossier)}
                                  >
                                    <i className="fe fe-check-circle"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('referred', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>ارجاع به مراجع</Tooltip>}>
                                  <Button
                                    variant="outline-danger"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleReferred(dossier)}
                                  >
                                    <i className="fe fe-send"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('ruling_issued', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>صدور حکم</Tooltip>}>
                                  <Button
                                    variant="outline-dark"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleRulingIssued(dossier)}
                                  >
                                    <i className="fe fe-bookmark"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {/* {canPerformAction('accept', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>تایید</Tooltip>}>
                                  <Button
                                    variant="outline-success"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleAccept(dossier)}
                                  >
                                    <i className="fe fe-check"></i>
                                  </Button>
                                </OverlayTrigger>
                              )}
                              {canPerformAction('reject', dossier) && (
                                <OverlayTrigger placement="top" overlay={<Tooltip>رد</Tooltip>}>
                                  <Button
                                    variant="outline-danger"
                                    style={{ maxWidth: '40px' }}
                                    onClick={() => handleReject(dossier)}
                                  >
                                    <i className="fe fe-x"></i>
                                  </Button>
                                </OverlayTrigger>
                              )} */}
                            </ButtonGroup>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

Inquiry.propTypes = {};

export default Inquiry;
