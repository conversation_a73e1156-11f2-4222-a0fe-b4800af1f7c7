// Form select options configuration
// Similar to STATUS_CONFIG, this centralizes all form dropdown options

import moment from 'moment-jalaali';

export const FORM_OPTIONS = {
  // Role types for reporters/reportees
  roleType: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'faculty_member', label: 'عضو هیئت علمی' },
    { value: 'student', label: 'دانشجو' },
    { value: 'non_faculty_member', label: 'عضو غیر هیئت علمی' },
    { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
    { value: 'institute_president', label: 'روسای موسسه' },
    { value: 'institute_vice_president', label: 'معاونان کل موسسه' },
    { value: 'board_member', label: 'اعضای هیئت رئیسه موسسه' },
    { value: 'trustee_member', label: 'اعضای هیئت امنای موسسه' },
    { value: 'ethics_committee_member', label: 'اعضای کارگروه‌های اخلاق در پژوهش مؤسسه‌ها' },
  ],

  // Academic status options
  academicStatus: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'graduated', label: 'فارغ‌التحصیل' },
    { value: 'studying', label: 'شاغل به تحصیل' },
  ],

  // Education level options
  educationLevel: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'phd', label: 'دکترای تخصصی (PHD)' },
    { value: 'professional_doctorate', label: 'دکترای حرفه‌ای' },
    { value: 'master', label: 'کارشناسی ارشد' },
    { value: 'bachelor', label: 'کارشناسی' },
    { value: 'associate', label: 'کاردانی' },
    { value: 'clinical_specialty', label: 'تخصص / فوق تخصص بالینی' },
  ],

  // Academic rank options
  academicRank: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'full_professor', label: 'استاد تمام' },
    { value: 'associate_professor', label: 'دانشیار' },
    { value: 'assistant_professor', label: 'استادیار' },
    { value: 'instructor', label: 'مربی' },
  ],

  // Scientific field options
  scientificField: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'clinical_sciences', label: 'علوم بالینی' },
    { value: 'basic_sciences', label: 'علوم پایه' },
    { value: 'non_clinical_sciences', label: 'علوم غیر بالینی' },
    { value: 'humanities', label: 'علوم انسانی' },
  ],

  // Organizational role options
  organizationalRole: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'faculty', label: 'هیات علمی' },
    { value: 'employee', label: 'کارمند' },
    { value: 'student', label: 'دانشجو' },
    { value: 'guest_researcher', label: 'پژوهشگر مهمان' },
    { value: 'invited_faculty', label: 'هیات علمی مدعو' },
    { value: 'other', label: 'سایر' },
  ],

  // Institute type options
  instituteType: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'faculty', label: 'دانشکده' },
    { value: 'research_center', label: 'مرکز تحقیقات' },
    { value: 'research_institute', label: 'پژوهشگاه' },
    { value: 'hospital', label: 'بیمارستان' },
    { value: 'other', label: 'سایر' },
  ],

  // Institute category options
  instituteCategory: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'type1', label: 'یک' },
    { value: 'type2', label: 'دو' },
    { value: 'type3', label: 'سه' },
    { value: 'non_medical', label: 'موسسات غیر علوم پزشکی' },
  ],

  // Reporter-reportee relationship options
  reporterReporteeRelation: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'professor_student', label: 'استاد-دانشجو' },
    { value: 'manager_employee', label: 'رئیس-کارمند' },
    { value: 'research_collaborator', label: 'همکار پژوهشی' },
    { value: 'patient_doctor', label: 'پزشک-بیمار' },
    { value: 'other', label: 'سایر' },
  ],

  // Whistleblower source options
  whistleblowerSource: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'journal_editor', label: 'مجله منتشرکننده پژوهش (دبیر یا داور یا خوانندگان)' },
    { value: 'scientific_council_reviewer', label: 'داور شورای علمی' },
    { value: 'ethical_reviewer', label: 'داور اخلاقی پژوهش' },
    { value: 'institute_manager', label: 'مدیر و مسئول موسسه محل انجام پژوهش' },
    { value: 'institute_colleagues', label: 'همکاران در موسسه' },
    { value: 'research_colleagues', label: 'همکاران آن پژوهش (همکار طرح پژوهشی)' },
    { value: 'co_author', label: 'نویسنده همکار در مقاله' },
    { value: 'student', label: 'دانشجو' },
    { value: 'thesis_advisor', label: 'استادان تیم پایان‌نامه' },
    { value: 'research_lead', label: 'مجری طرح پژوهشی' },
    { value: 'research_participant', label: 'شرکت‌کنندگان در پژوهش (بیمار یا آزمودنی‌ها)' },
    { value: 'research_sponsor', label: 'حامی مالی پژوهش' },
    { value: 'news_media', label: 'رسانه‌های خبری' },
    { value: 'research_social_media', label: 'شبکه‌های اجتماعی پژوهشی' },
    { value: 'pubpeer', label: 'Pubpeer' },
    { value: 'independent_whistleblower', label: 'افشاگران مستقل جامعه' },
    { value: 'anonymous', label: 'فرد ناشناس' },
    { value: 'international_site', label: 'سایت‌های بین‌المللی' },
    { value: 'other', label: 'سایر' },
  ],

  // Report delay options
  reportDelay: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'less_than_one_year', label: 'کمتر از یکسال' },
    { value: 'one_year', label: 'یکسال' },
    { value: 'two_years', label: 'دو سال' },
    { value: 'more_than_two_years', label: 'بیش از دو سال' },
  ],

  // Report recipient options
  reportRecipient: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'institute_ethics_committee', label: 'کارگروه اخلاق در پژوهش موسسه' },
    { value: 'ministry_ethics_committee', label: 'کارگروه اخلاق در پژوهش وزارتخانه' },
    { value: 'security_office', label: 'حراست' },
    { value: 'inspection_office', label: 'اداره بازرسی و پاسخگویی به شکایات' },
    { value: 'violation_committee', label: 'هیات‌های رسیدگی به تخلف' },
    { value: 'other', label: 'سایر' },
  ],

  // Report channel options
  reportChannel: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'phone_call', label: 'تماس تلفنی' },
    { value: 'email', label: 'ایمیل' },
    { value: 'formal_letter', label: 'نامه رسمی' },
    { value: 'social_media', label: 'شبکه‌های اجتماعی مجازی' },
  ],

  // Research stage options
  researchStage: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'before_research', label: 'پیش از شروع پژوهش' },
    { value: 'during_research', label: 'حین انجام پژوهش' },
    { value: 'after_publication', label: 'پس از پایان پژوهش و انتشار آن' },
  ],

  // Reporter role options (subset of roleType for Step2)
  reporterRoleType: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'faculty_member', label: 'عضو هیئت علمی' },
    { value: 'student', label: 'دانشجو' },
    { value: 'non_faculty_member', label: 'عضو غیر هیئت علمی' },
    { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
  ],

  // Authority options for referral to competent authorities
  authorities: [
    { value: '', label: 'انتخاب کنید' },
    { value: 'PROFESSOR_SELECTION', label: 'هیات گزینش استاد' },
    { value: 'FACULTY_RECRUITMENT', label: 'هیات جذب اعضای هیات علمی' },
    { value: 'UNIVERSITY_EVALUATION', label: 'هیات ممیزه دانشگاه و موسسه آموزش عالی' },
    { value: 'STUDENT_SELECTION', label: 'هیات گزینش دانشجو' },
    { value: 'STUDENT_DISCIPLINARY', label: 'کمیته انضباطی دانشجویان' },
    { value: 'STAFF_VIOLATION', label: 'هیات رسیدگی به تخلفات اداری کارکنان' },
    { value: 'MEDICAL_COUNCIL', label: 'سازمان نظام پزشکی' },
    { value: 'NURSING_COUNCIL', label: 'نظام پرستاری' },
    { value: 'AFFILIATED_INSTITUTE', label: 'موسسات آموزشی یا تحقیقاتی یا سازمان یا نهاد متبوع شخص متخلف' },
    { value: 'CULTURAL_REVOLUTION', label: 'شورای عالی انقلاب فرهنگی' },
    { value: 'JUDICIARY', label: 'قوه قضاییه' },
  ],

  // Violation verification options
  violationVerification: [
    { value: 'confirmed', label: 'احراز' },
    { value: 'not_confirmed', label: 'عدم احراز' },
  ],

  // Violation types
  violationTypes: [
    { value: 'plagiarism', label: 'سرقت ادبی' },
    { value: 'data_fabrication', label: 'داده‌سازی' },
    { value: 'data_falsification', label: 'تحریف داده‌ها' },
    { value: 'peer_review_manipulation', label: 'دستکاری داوری' },
    { value: 'citation_manipulation', label: 'دستکاری استنادها' },
    { value: 'image_manipulation', label: 'دستکاری تصاویر' },
    { value: 'authorship_misconduct', label: 'رعایت نکردن معیارهای نویسندگی' },
    { value: 'overlapping_publication', label: 'انتشار هم‌پوشان' },
    { value: 'duplicate_publication', label: 'انتشار تکراری' },
    { value: 'simultaneous_submission', label: 'ارسال هم‌زمان دستنوشته' },
    { value: 'fake_ethics_code', label: 'کد اخلاق جعلی' },
    { value: 'fake_clinical_trial_code', label: 'کد کارآزمایی بالینی جعلی' },
    { value: 'other', label: 'سایر' },
  ],

  // Benefit from violation options
  benefitFromViolation: [
    { value: 'benefited', label: 'انتفاع' },
    { value: 'not_benefited', label: 'عدم انتفاع' },
  ],

  // Faculty benefit types
  facultyBenefitTypes: [
    { value: 'promotion', label: 'ارتقاء رتبه' },
    { value: 'group_promotion', label: 'ارتقای گروه' },
    { value: 'research_funding', label: 'دریافت هزینه پژوهشی' },
    { value: 'article_incentive', label: 'دریافت حق‌التشویق مقاله' },
    { value: 'other', label: 'سایر' },
  ],

  // Student benefit types
  studentBenefitTypes: [
    { value: 'thesis_grade', label: 'اخذ نمره پایان‌نامه' },
    { value: 'research_funding', label: 'دریافت هزینه پژوهشی' },
    { value: 'article_incentive', label: 'دریافت حق‌التشویق مقاله' },
    { value: 'other', label: 'سایر' },
  ],

  // Employee benefit types
  employeeBenefitTypes: [
    { value: 'promotion', label: 'ارتقاء طبقه/رتبه' },
    { value: 'evaluation_grade', label: 'اخذ نمره ارزشیابی' },
    { value: 'research_funding', label: 'دریافت هزینه پژوهشی' },
    { value: 'article_incentive', label: 'دریافت حق‌التشویق مقاله' },
    { value: 'other', label: 'سایر' },
  ],

  // Researcher benefit types
  researcherBenefitTypes: [
    { value: 'research_funding', label: 'دریافت هزینه پژوهشی' },
    { value: 'article_incentive', label: 'دریافت حق‌التشویق مقاله' },
    { value: 'other', label: 'سایر' },
  ],

  // Research stages for violation
  violationResearchStages: [
    { value: 'pre_research', label: 'پیش از شروع پژوهش' },
    { value: 'during_research', label: 'حین انجام پژوهش' },
    { value: 'post_research', label: 'پس از پایان پژوهش و انتشار آن' },
  ],

  // Violation patterns
  violationPatterns: [
    { value: 'individual', label: 'موردی' },
    { value: 'organized', label: 'سازمان‌یافته' },
  ],

  // Violation effects
  violationEffects: [
    { value: 'research_findings', label: 'یافته‌های پژوهش' },
    { value: 'publications', label: 'انتشارات' },
    { value: 'human_animal_subjects', label: 'آزمودنی‌های انسانی یا حیوانی پژوهش' },
    { value: 'laboratory', label: 'آزمایشگاه' },
    { value: 'project', label: 'پروژه' },
    { value: 'individual_reputation', label: 'اعتبار علمی فرد' },
    { value: 'colleagues_reputation', label: 'اعتبار علمی همکاران پژوهش' },
    { value: 'institute_reputation', label: 'اعتبار علمی موسسه' },
    { value: 'institute_funding', label: 'اعتبارات مالی موسسه' },
  ],
};

// Helper function to get options by key
export const getFormOptions = (optionKey) => {
  return FORM_OPTIONS[optionKey] || [];
};

// Helper function to get option label by value
export const getOptionLabel = (optionKey, value) => {
  const options = FORM_OPTIONS[optionKey] || [];
  const option = options.find((opt) => opt.value === value);
  return option ? option.label : value;
};

// Helper function to generate violation years (last 20 years in Persian calendar)
export const getViolationYears = () => {
  const currentPersianYear = moment().jYear();
  return Array.from({ length: 20 }, (_, i) => ({
    value: currentPersianYear - i,
    label: (currentPersianYear - i).toString(),
  }));
};

// Helper function to generate violation counts (1-20)
export const getViolationCounts = () => {
  return Array.from({ length: 20 }, (_, i) => ({
    value: i + 1,
    label: `${i + 1} مورد`,
  }));
};
