import { Card, Row, Col, Badge, Table } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { formatJalaliDate } from 'utils/helper.js';

// Stage configuration - based on actual dossier structure
const STAGE_CONFIG = {
  initial_opinion: {
    label: 'نظریه بدوی کارگروه موسسه',
    color: 'info',
    icon: 'fe-file-text',
    fields: ['initial_opinion'],
  },
  ministry_opinion: {
    label: 'نظریه کارگروه وزارتی',
    color: 'purple',
    icon: 'fe-shield',
    fields: ['ministry_opinion'],
  },
  final_opinion: {
    label: 'نظریه نهایی کارگروه موسسه',
    color: 'dark',
    icon: 'fe-check-circle',
    fields: ['final_opinion'],
  },
  violation_details: {
    label: 'جزئیات تخلف',
    color: 'secondary',
    icon: 'fe-alert-triangle',
    fields: ['violation_details'],
  },
  referred: {
    label: 'ارجاع به مراجع ذیصلاح',
    color: 'danger',
    icon: 'fe-send',
    fields: ['referred'],
  },
  ruling_issued: {
    label: 'صدور حکم/رای مراجع رسیدگی کننده',
    color: 'teal',
    icon: 'fe-award',
    fields: ['ruling_issued'],
  },
};

// Field labels mapping for different data types
const FIELD_LABELS = {
  opinion: 'نظریه',
  statements: 'بیانیه',
  session_date: 'تاریخ جلسه',
  session_members: 'اعضای جلسه',
  opinion_announcement_date: 'تاریخ اعلام نظریه',
  reviewing_authority: 'مرجع بررسی',
  violation_status: 'وضعیت تخلف',
  violation_nature: 'ماهیت تخلف',
  violation_count: 'تعداد تخلف',
  benefit_status: 'وضعیت منفعت',
  research_stage: 'مرحله تحقیق',
  violation_year: 'سال تخلف',
  faculty_benefit: 'منفعت هیئت علمی',
  student_benefit: 'منفعت دانشجو',
  employee_benefit: 'منفعت کارمند',
  independent_researcher_benefit: 'منفعت محقق مستقل',
};

// Authority labels mapping
const AUTHORITY_LABELS = {
  STUDENT_SELECTION: 'انتخاب دانشجو',
  STUDENT_DISCIPLINARY: 'انضباطی دانشجو',
  FACULTY_DISCIPLINARY: 'انضباطی هیئت علمی',
  ETHICS_COMMITTEE: 'کمیته اخلاق',
  RESEARCH_COUNCIL: 'شورای پژوهش',
};

const DossierStageDetails = ({ dossier }) => {
  // Get all stage data from dossier
  const getStageData = (stageName) => {
    const stageConfig = STAGE_CONFIG[stageName];
    if (!stageConfig) return null;

    // Check if this stage has any data
    const hasData = stageConfig.fields.some((field) => {
      const fieldData = dossier[field];
      return (
        fieldData &&
        typeof fieldData === 'object' &&
        Object.keys(fieldData).length > 0 &&
        Object.values(fieldData).some((value) => value !== null && value !== '')
      );
    });

    if (!hasData) return null;

    return {
      config: stageConfig,
      data: stageConfig.fields.reduce((acc, field) => {
        const fieldData = dossier[field];
        if (fieldData && typeof fieldData === 'object' && Object.keys(fieldData).length > 0) {
          acc[field] = fieldData;
        }
        return acc;
      }, {}),
    };
  };

  // Get all stages that have data
  const stagesWithData = Object.keys(STAGE_CONFIG)
    .map((stageName) => ({ stageName, stageData: getStageData(stageName) }))
    .filter(({ stageData }) => stageData !== null);

  console.log(stagesWithData);

  if (stagesWithData.length === 0) {
    return null; // Don't render anything if no stage data exists
  }

  // Helper function to render field value
  const renderFieldValue = (key, value) => {
    if (key === 'session_date' || key === 'opinion_announcement_date') {
      return formatJalaliDate(value);
    }
    if (key === 'reviewing_authority') {
      return AUTHORITY_LABELS[value] || value;
    }
    if (key === 'violation_status') {
      const statusLabels = {
        confirmed: 'تایید شده',
        not_confirmed: 'تایید نشده',
        under_review: 'در حال بررسی',
      };
      return statusLabels[value] || value;
    }
    return value || '-';
  };

  // Helper function to render stage data
  const renderStageData = (stageData) => {
    return Object.entries(stageData.data).map(([field, fieldData]) => (
      <div key={field} className="mb-4">
        <h6 className="mb-3 text-primary">
          <i className="fe fe-folder me-2"></i>
          {stageData.config.label}
        </h6>

        {/* Opinion and Statements - Full width */}
        {(fieldData.opinion || fieldData.statements) && (
          <Row className="mb-3">
            {fieldData.opinion && (
              <Col md={fieldData.statements ? 6 : 12} className="mb-3">
                <div>
                  <h6 className="mb-2">
                    <i className="fe fe-message-square me-2"></i>
                    {FIELD_LABELS.opinion}
                  </h6>
                  <div className="p-3 bg-light rounded">
                    <p className="mb-0" style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                      {fieldData.opinion}
                    </p>
                  </div>
                </div>
              </Col>
            )}
            {fieldData.statements && (
              <Col md={fieldData.opinion ? 6 : 12} className="mb-3">
                <div>
                  <h6 className="mb-2">
                    <i className="fe fe-file-text me-2"></i>
                    {FIELD_LABELS.statements}
                  </h6>
                  <div className="p-3 bg-light rounded">
                    <p className="mb-0" style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                      {fieldData.statements}
                    </p>
                  </div>
                </div>
              </Col>
            )}
          </Row>
        )}

        {/* Other fields - Table format */}
        <Table borderless className="mb-0">
          <tbody>
            {Object.entries(fieldData).map(([key, value]) => {
              if (key === 'opinion' || key === 'statements' || value === null || value === '') {
                return null;
              }
              return (
                <tr key={key}>
                  <td className="fw-bold text-muted" style={{ width: '40%' }}>
                    {FIELD_LABELS[key] || key}:
                  </td>
                  <td>{renderFieldValue(key, value)}</td>
                </tr>
              );
            })}
          </tbody>
        </Table>
      </div>
    ));
  };

  return (
    <div>
      {stagesWithData.map(({ stageName, stageData }) => (
        <Card key={stageName} className="mb-4">
          <Card.Header>
            <Card.Title className="mb-0">
              <i className={`${stageData.config.icon} me-2`}></i>
              {stageData.config.label}
              <Badge bg={stageData.config.color} className="ms-2">
                تکمیل شده
              </Badge>
            </Card.Title>
          </Card.Header>
          <Card.Body>
            {renderStageData(stageData)}

            {/* Show stage timestamp if available */}
            {dossier.updated_at && (
              <div className="mt-3 pt-3 border-top">
                <small className="text-muted">
                  <i className="fe fe-clock me-1"></i>
                  آخرین به‌روزرسانی: {formatJalaliDate(dossier.updated_at)}
                </small>
              </div>
            )}
          </Card.Body>
        </Card>
      ))}
    </div>
  );
};

DossierStageDetails.propTypes = {
  dossier: PropTypes.object.isRequired,
};

export default DossierStageDetails;
