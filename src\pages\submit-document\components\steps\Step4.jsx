import React, { Fragment } from 'react';
import { Form, FormGroup } from 'react-bootstrap';
import Select from 'react-select';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';
import { formatDate } from 'utils/helper.js';
import { FORM_OPTIONS } from 'utils/formOptions.js';

const Step4 = ({ validationErrors }) => {
  const { step4, setStep4 } = useSubmitDocumentFormStore();

  return (
    <section className="card-body Basicwizard">
      <Form.Group className="control-group form-group pos-relative">
        <Form.Label className="form-label">تاریخ دریافت گزارش توسط کارگروه</Form.Label>
        <DatePicker
          value={step4.reportReceivedDate ? new Date(step4.reportReceivedDate) : null}
          calendar={persian}
          locale={persian_fa}
          calendarPosition="bottom-right"
          containerClassName={'form-control'}
          style={{
            background: 'transparent',
            width: '100%',
            boxShadow: 'none!important',
            outline: 'none',
            border: 'none',
          }}
          onChange={(date) => {
            validationErrors.reportReceivedDate = null;
            const formattedDate = date ? formatDate(new Date(date)) : '';
            setStep4({ reportReceivedDate: formattedDate });
          }}
          placeholder="تاریخ دریافت گزارش را انتخاب کنید"
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportReceivedDate ? 'block' : 'none' }}>
          {validationErrors.reportReceivedDate}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">تخلف چه مدت پس از وقوع، گزارش شده است</Form.Label>
        <Select
          options={FORM_OPTIONS.reportDelay}
          placeholder="مدت زمان را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={FORM_OPTIONS.reportDelay.find((option) => option.value === step4.reportDelay)}
          onChange={(e) => {
            validationErrors.reportDelay = null;
            setStep4({ reportDelay: e.value });
          }}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportDelay ? 'block' : 'none' }}>
          {validationErrors.reportDelay}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">نحوه اعلام گزارش تخلف به کارگروه</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-method"
              value="self_report"
              checked={step4.reportMethod === 'self_report'}
              onChange={() => {
                validationErrors.reportMethod = null;
                setStep4({ reportMethod: 'self_report', whistleblowerSource: null });
              }}
            />
            <span className="custom-control-label">خودگزارشی فرد متخلف</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-method"
              value="whistleblower"
              checked={step4.reportMethod === 'whistleblower'}
              onChange={() => {
                validationErrors.reportMethod = null;
                setStep4({ reportMethod: 'whistleblower' });
              }}
            />
            <span className="custom-control-label">افشاگری دیگران</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reportMethod ? 'block' : 'none' }}>
          {validationErrors.reportMethod}
        </div>
      </Form.Group>

      {step4.reportMethod === 'whistleblower' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">منبع افشاگری</Form.Label>
            <Select
              options={FORM_OPTIONS.whistleblowerSource}
              placeholder="منبع افشاگری را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={FORM_OPTIONS.whistleblowerSource.find((option) => option.value === step4.whistleblowerSource)}
              onChange={(e) => {
                validationErrors.whistleblowerSource = null;
                setStep4({ whistleblowerSource: e.value });
              }}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.whistleblowerSource ? 'block' : 'none' }}
            >
              {validationErrors.whistleblowerSource}
            </div>
          </Form.Group>
        </Fragment>
      )}

      <Form.Group className="form-group">
        <Form.Label className="form-label">هویت گزارش‌دهنده</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-identity"
              value="known"
              checked={step4.reporterIdentity === 'known'}
              onChange={() => {
                validationErrors.reporterIdentity = null;
                setStep4({ reporterIdentity: 'known' });
              }}
            />
            <span className="custom-control-label">معلوم</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-identity"
              value="unknown"
              checked={step4.reporterIdentity === 'unknown'}
              onChange={() => {
                validationErrors.reporterIdentity = null;
                setStep4({ reporterIdentity: 'unknown' });
              }}
            />
            <span className="custom-control-label">نامعلوم</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reporterIdentity ? 'block' : 'none' }}>
          {validationErrors.reporterIdentity}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">نقش گزارش‌دهنده تخلف پژوهشی</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-role"
              value="whistleblower"
              checked={step4.reporterViolationRole === 'whistleblower'}
              onChange={() => {
                validationErrors.reporterViolationRole = null;
                setStep4({ reporterViolationRole: 'whistleblower' });
              }}
            />
            <span className="custom-control-label">افشاگر (whistleblower)</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-role"
              value="complainant"
              checked={step4.reporterViolationRole === 'complainant'}
              onChange={() => {
                validationErrors.reporterViolationRole = null;
                setStep4({ reporterViolationRole: 'complainant' });
              }}
            />
            <span className="custom-control-label">شاکی (complainant)</span>
          </Form.Label>
        </div>
        <div
          className="invalid-feedback"
          style={{ display: validationErrors.reporterViolationRole ? 'block' : 'none' }}
        >
          {validationErrors.reporterViolationRole}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">ذینفعی گزارش‌دهنده در این پژوهش</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-stakeholder"
              value="stakeholder"
              checked={step4.reporterStakeholder === 'stakeholder'}
              onChange={() => {
                validationErrors.reporterStakeholder = null;
                setStep4({ reporterStakeholder: 'stakeholder' });
              }}
            />
            <span className="custom-control-label">ذینفع</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-stakeholder"
              value="non_stakeholder"
              checked={step4.reporterStakeholder === 'non_stakeholder'}
              onChange={() => {
                validationErrors.reporterStakeholder = null;
                setStep4({ reporterStakeholder: 'non_stakeholder' });
              }}
            />
            <span className="custom-control-label">غیر ذینفع</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reporterStakeholder ? 'block' : 'none' }}>
          {validationErrors.reporterStakeholder}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">رابطه گزارش‌دهنده با گزارش‌شونده</Form.Label>
        <Select
          options={FORM_OPTIONS.reporterReporteeRelation}
          placeholder="رابطه را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={FORM_OPTIONS.reporterReporteeRelation.find(
            (option) => option.value === step4.reporterReportedRelation
          )}
          onChange={(e) => {
            validationErrors.reporterReportedRelation = null;
            setStep4({ reporterReportedRelation: e.value });
          }}
        />
        <div
          className="invalid-feedback"
          style={{ display: validationErrors.reporterReportedRelation ? 'block' : 'none' }}
        >
          {validationErrors.reporterReportedRelation}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">مرجع دریافت کننده گزارش تخلف</Form.Label>
        <Select
          options={FORM_OPTIONS.reportRecipient}
          placeholder="مرجع را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={FORM_OPTIONS.reportRecipient.find((option) => option.value === step4.reportRecipient)}
          onChange={(e) => {
            validationErrors.reportRecipient = null;
            setStep4({ reportRecipient: e.value });
          }}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportRecipient ? 'block' : 'none' }}>
          {validationErrors.reportRecipient}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">شکل گزارش تخلف</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-format"
              value="written"
              checked={step4.reportFormat === 'written'}
              onChange={() => {
                validationErrors.reportFormat = null;
                setStep4({ reportFormat: 'written' });
              }}
            />
            <span className="custom-control-label">کتبی</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-format"
              value="electronic"
              checked={step4.reportFormat === 'electronic'}
              onChange={() => {
                validationErrors.reportFormat = null;
                setStep4({ reportFormat: 'electronic' });
              }}
            />
            <span className="custom-control-label">الکترونیکی</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reportFormat ? 'block' : 'none' }}>
          {validationErrors.reportFormat}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">مسیر ارسال گزارش تخلف</Form.Label>
        <Select
          options={FORM_OPTIONS.reportChannel}
          placeholder="مسیر ارسال را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={FORM_OPTIONS.reportChannel.find((option) => option.value === step4.reportChannel)}
          onChange={(e) => {
            validationErrors.reportChannel = null;
            setStep4({ reportChannel: e.value });
          }}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportChannel ? 'block' : 'none' }}>
          {validationErrors.reportChannel}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">گزارش تخلف پژوهشی در کدام مرحله انجام پژوهش</Form.Label>
        <Select
          options={FORM_OPTIONS.researchStage}
          placeholder="مرحله پژوهش را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={FORM_OPTIONS.researchStage.find((option) => option.value === step4.researchStage)}
          onChange={(e) => {
            validationErrors.researchStage = null;
            setStep4({ researchStage: e.value });
          }}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.researchStage ? 'block' : 'none' }}>
          {validationErrors.researchStage}
        </div>
      </Form.Group>
    </section>
  );
};

export default Step4;
